import { useState, useEffect } from 'react';
import SearchForm from '../components/SearchForm';
import SearchResults from '../components/SearchResults';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import apiService from '../services/api';
import './SearchPage.css';

const SearchPage = () => {
  const [searchResults, setSearchResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchParams, setSearchParams] = useState({
    query: '',
    page: 1,
    limit: 20,
    fromYear: '',
    toYear: '',
    openAccess: false,
    minCitations: ''
  });

  const handleSearch = async (params) => {
    if (!params.query.trim()) {
      setError('Please enter a search query');
      return;
    }

    setLoading(true);
    setError(null);
    setSearchParams(params);

    try {
      const response = await apiService.searchPapers(params);
      setSearchResults(response.data);
    } catch (err) {
      setError(err.message || 'Failed to search papers. Please try again.');
      setSearchResults(null);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    const newParams = { ...searchParams, page: newPage };
    handleSearch(newParams);
  };

  return (
    <div className="search-page">
      <div className="search-page-container">
        <div className="search-header">
          <h2>Discover Research Papers</h2>
          <p>Search through millions of academic papers and research publications</p>
        </div>

        <SearchForm onSearch={handleSearch} loading={loading} />

        {error && <ErrorMessage message={error} />}

        {loading && <LoadingSpinner />}

        {searchResults && !loading && (
          <SearchResults 
            results={searchResults}
            onPageChange={handlePageChange}
            currentPage={searchParams.page}
          />
        )}

        {!searchResults && !loading && !error && (
          <div className="welcome-message">
            <div className="welcome-content">
              <h3>🔍 Start Your Research Journey</h3>
              <p>Enter keywords, author names, or topics to find relevant research papers.</p>
              <div className="search-tips">
                <h4>Search Tips:</h4>
                <ul>
                  <li>Use specific keywords for better results</li>
                  <li>Try author names like "Einstein" or "Hawking"</li>
                  <li>Search for topics like "machine learning" or "climate change"</li>
                  <li>Use filters to narrow down your results</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchPage;
