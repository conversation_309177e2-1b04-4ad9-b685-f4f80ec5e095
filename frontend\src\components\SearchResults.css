.search-results {
  margin-top: 2rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0 0.5rem;
}

.results-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.results-header p {
  margin: 0;
  color: var(--text-secondary);
  font-weight: 500;
}

.papers-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.paper-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
  border: 1px solid var(--border-light);
}

.paper-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.paper-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.paper-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
  line-height: 1.4;
  flex: 1;
  transition: color 0.2s ease;
}

.paper-title:hover {
  color: var(--primary-color);
}

.paper-badges {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.badge.open-access {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge.year {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.paper-authors,
.paper-venue {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: baseline;
}

.authors-label,
.venue-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  flex-shrink: 0;
}

.authors-text,
.venue-text {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
}

.paper-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stat-icon {
  font-size: 0.875rem;
}

.stat-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.paper-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.view-details-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.view-details-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  color: white;
}

.external-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.external-link:hover {
  background: var(--primary-color);
  color: white;
}

.external-link.pdf-link {
  color: var(--error-color);
  border-color: var(--error-color);
}

.external-link.pdf-link:hover {
  background: var(--error-color);
  color: white;
}

.no-results {
  text-align: center;
  padding: 3rem;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.no-results h3 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.no-results p {
  color: var(--text-secondary);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .paper-card {
    padding: 1rem;
  }
  
  .paper-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .paper-badges {
    align-self: flex-start;
  }
  
  .paper-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .paper-actions {
    gap: 0.75rem;
  }
  
  .view-details-btn,
  .external-link {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

@media (max-width: 480px) {
  .paper-card {
    padding: 0.75rem;
  }
  
  .paper-title {
    font-size: 1.125rem;
  }
  
  .paper-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-details-btn,
  .external-link {
    text-align: center;
  }
}
