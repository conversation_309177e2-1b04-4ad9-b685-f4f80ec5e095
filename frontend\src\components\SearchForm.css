.search-form-container {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-lg);
  margin-bottom: 2rem;
}

.search-form {
  max-width: 800px;
  margin: 0 auto;
}

.search-input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-input {
  flex: 1;
  padding: 1rem;
  font-size: 1.125rem;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-button {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  white-space: nowrap;
  min-width: 120px;
}

.search-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.filter-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.filter-toggle {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.filter-toggle:hover {
  background: var(--border-light);
  transform: none;
  box-shadow: none;
}

.clear-filters {
  background: var(--error-color);
  color: white;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: var(--radius-md);
}

.filters {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-top: 1rem;
  border: 1px solid var(--border-light);
}

.filter-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.filter-input {
  padding: 0.75rem;
  font-size: 0.875rem;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
}

.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 0;
}

.filter-checkbox {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.checkmark {
  position: relative;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-form-container {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .search-input-group {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .search-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .filters {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .search-form-container {
    padding: 1rem;
  }
  
  .search-input {
    font-size: 1rem;
    padding: 0.875rem;
  }
  
  .search-button {
    font-size: 0.875rem;
    padding: 0.875rem 1rem;
  }
}
