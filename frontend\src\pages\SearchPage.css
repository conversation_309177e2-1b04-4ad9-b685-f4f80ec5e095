.search-page {
  min-height: calc(100vh - 80px);
  padding: 2rem 0;
}

.search-page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.search-header {
  text-align: center;
  margin-bottom: 3rem;
}

.search-header h2 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.search-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.welcome-message {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: 3rem;
  margin-top: 3rem;
  box-shadow: var(--shadow-md);
  text-align: center;
}

.welcome-content h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.welcome-content p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  color: var(--text-secondary);
}

.search-tips {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.search-tips h4 {
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-size: 1.125rem;
}

.search-tips ul {
  margin: 0;
  padding-left: 1.5rem;
}

.search-tips li {
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-page {
    padding: 1rem 0;
  }
  
  .search-page-container {
    padding: 0 0.5rem;
  }
  
  .search-header {
    margin-bottom: 2rem;
  }
  
  .search-header h2 {
    font-size: 2rem;
  }
  
  .search-header p {
    font-size: 1rem;
  }
  
  .welcome-message {
    padding: 2rem 1rem;
    margin-top: 2rem;
  }
  
  .search-tips {
    padding: 1rem;
  }
}
