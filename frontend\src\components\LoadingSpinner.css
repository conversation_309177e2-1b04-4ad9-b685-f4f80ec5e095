.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  margin: 2rem 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  color: var(--text-secondary);
  font-weight: 500;
  margin: 0;
  text-align: center;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .loading-spinner {
    padding: 2rem;
  }
  
  .spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
  }
  
  .loading-message {
    font-size: 0.875rem;
  }
}
