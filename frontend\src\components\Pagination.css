.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 3rem;
  padding: 2rem 0;
}

.pagination-btn {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.page-numbers {
  display: flex;
  gap: 0.5rem;
}

.page-number {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  min-width: 40px;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.page-number:hover:not(:disabled):not(.ellipsis) {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.page-number.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.page-number.ellipsis {
  background: transparent;
  border: none;
  cursor: default;
  box-shadow: none;
}

.page-number.ellipsis:hover {
  transform: none;
  box-shadow: none;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .pagination {
    gap: 0.5rem;
    margin-top: 2rem;
    padding: 1.5rem 0;
  }
  
  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .page-number {
    padding: 0.4rem 0.6rem;
    font-size: 0.875rem;
    min-width: 36px;
  }
}

@media (max-width: 480px) {
  .pagination {
    flex-direction: column;
    gap: 1rem;
  }
  
  .page-numbers {
    order: -1;
  }
  
  .pagination-btn {
    width: 100%;
    max-width: 200px;
  }
}
