.paper-detail-page {
  min-height: calc(100vh - 80px);
  padding: 2rem 0;
  background: var(--bg-secondary);
}

.paper-detail-page .container {
  max-width: 900px;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 2rem;
  padding: 0.5rem 0;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: var(--primary-dark);
}

.paper-detail {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.paper-header {
  padding: 2rem;
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.paper-title {
  font-size: 2rem;
  line-height: 1.3;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.paper-meta {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.meta-row {
  display: flex;
  gap: 1rem;
}

.meta-label {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 100px;
  flex-shrink: 0;
}

.meta-value {
  color: var(--text-secondary);
  line-height: 1.5;
}

.paper-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.badge {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
}

.badge.open-access {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.paper-section {
  padding: 2rem;
  border-bottom: 1px solid var(--border-light);
}

.paper-section:last-child {
  border-bottom: none;
}

.paper-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
  display: inline-block;
}

.abstract-text {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--text-secondary);
  text-align: justify;
}

.external-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.external-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border: 2px solid var(--primary-color);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  background: var(--bg-primary);
}

.external-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.external-link.pdf-link {
  color: var(--error-color);
  border-color: var(--error-color);
}

.external-link.pdf-link:hover {
  background: var(--error-color);
  color: white;
}

.concepts {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.concept-tag {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid var(--border-light);
}

.references-note {
  color: var(--text-secondary);
  font-style: italic;
}

.not-found {
  text-align: center;
  padding: 3rem;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

.not-found h2 {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.not-found p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .paper-detail-page {
    padding: 1rem 0;
  }
  
  .paper-detail-page .container {
    padding: 0 0.5rem;
  }
  
  .back-link {
    margin-bottom: 1rem;
  }
  
  .paper-header {
    padding: 1.5rem;
  }
  
  .paper-title {
    font-size: 1.5rem;
  }
  
  .meta-row {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .meta-label {
    min-width: auto;
    font-size: 0.875rem;
  }
  
  .meta-value {
    font-size: 0.875rem;
  }
  
  .paper-section {
    padding: 1.5rem;
  }
  
  .paper-section h2 {
    font-size: 1.25rem;
  }
  
  .abstract-text {
    font-size: 1rem;
    text-align: left;
  }
  
  .external-links {
    gap: 0.75rem;
  }
  
  .external-link {
    padding: 0.6rem 0.8rem;
    font-size: 0.875rem;
  }
  
  .concepts {
    gap: 0.5rem;
  }
  
  .concept-tag {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

@media (max-width: 480px) {
  .paper-header {
    padding: 1rem;
  }
  
  .paper-title {
    font-size: 1.25rem;
  }
  
  .paper-section {
    padding: 1rem;
  }
  
  .external-link {
    justify-content: center;
    text-align: center;
  }
}
